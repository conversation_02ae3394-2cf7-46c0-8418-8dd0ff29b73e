import React from 'react';
import { DivideIcon as LucideIcon, ArrowRight } from 'lucide-react';

interface ServiceCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  features: string[];
  ctaText: string;
  gradient: string;
}

export default function ServiceCard({ 
  icon: Icon, 
  title, 
  description, 
  features, 
  ctaText, 
  gradient 
}: ServiceCardProps) {
  return (
    <div className="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-102 overflow-hidden">
      <div className={`h-2 bg-gradient-to-r ${gradient}`}></div>
      
      <div className="p-8">
        <div className={`inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-r ${gradient} mb-6`}>
          <Icon className="h-8 w-8 text-white" />
        </div>
        
        <h3 className="text-xl font-bold text-gray-900 mb-4">{title}</h3>
        <p className="text-gray-600 mb-6 leading-relaxed">{description}</p>
        
        <ul className="space-y-2 mb-6">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center text-sm text-gray-700">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3 flex-shrink-0"></div>
              {feature}
            </li>
          ))}
        </ul>
        
        <button className="flex items-center justify-center w-full bg-gray-50 hover:bg-blue-50 text-blue-600 py-3 px-4 rounded-lg font-medium transition-colors duration-200 group-hover:bg-blue-500 group-hover:text-white">
          <span>{ctaText}</span>
          <ArrowRight className="h-4 w-4 ml-2 transform group-hover:translate-x-1 transition-transform" />
        </button>
      </div>
    </div>
  );
}