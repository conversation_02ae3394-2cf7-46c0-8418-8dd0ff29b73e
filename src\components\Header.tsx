import React, { useState } from "react";
import { Menu, X } from "lucide-react";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/10 backdrop-blur-md border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-4 ml-4">
            <div className="flex items-center space-x-4">
              <img
                src="/src/logos/LOGO_TARATIC_V8__solo_logo.svg"
                alt="TARATIC Logo"
                className="h-8 w-8"
              />
              <img
                src="/src/logos/LOGO_TARATIC_V8__solo_letras.svg"
                alt="TARATIC"
                className="h-6"
              />
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a
              href="#servicios"
              className="text-blue-500 hover:text-blue-500 transition-colors text-sm font-medium"
              style={{
                textShadow: "0 0 10px rgba(255, 255, 255, 0.8)",
              }}
            >
              Servicios
            </a>
            <a
              href="#contacto"
              className="text-blue-500 hover:text-blue-500 transition-colors text-sm font-medium"
              style={{
                textShadow: "0 0 10px rgba(255, 255, 255, 0.8)",
              }}
            >
              Contacto
            </a>
            <button className=" text-blue-500 bg-white/20 hover:bg-blue-600 hover:text-white px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 backdrop-blur-sm border border-blue-600">
              Consulta Gratuita
            </button>
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="text-blue-500 hover:text-blue-500 transition-colors p-2"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <div className="md:hidden absolute top-16 left-0 right-0 bg-white/90 backdrop-blur-md border-b border-white/30 shadow-lg">
            <nav className="px-4 py-6 space-y-4">
              <a
                href="#servicios"
                onClick={closeMenu}
                className="block text-blue-700 hover:text-blue-500 transition-colors text-base font-medium py-2"
                style={{
                  textShadow: "0 0 10px rgba(255, 255, 255, 0.8)",
                }}
              >
                Servicios
              </a>
              <a
                href="#contacto"
                onClick={closeMenu}
                className="block text-blue-700 hover:text-blue-500 transition-colors text-base font-medium py-2"
                style={{
                  textShadow: "0 0 10px rgba(255, 255, 255, 0.8)",
                }}
              >
                Contacto
              </a>
              <button
                onClick={closeMenu}
                className="w-full bg-blue-700 hover:bg-blue-500 text-white px-4 py-3 rounded-full text-base font-medium transition-all duration-200 shadow-lg mt-4"
              >
                Consulta Gratuita
              </button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
